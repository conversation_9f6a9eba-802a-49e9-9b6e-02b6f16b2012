import classNamesBind from 'classnames/bind'
import React, { useState } from 'react'
import styles from './employee-statistics.module.scss'
import { useEmployeeStatistics } from './use-employee-statistics'
import { EmployeeInformation, EmployeeNotice, getFullUserName } from '@/entities/employee'
import { Tags } from '@/shared/components/tags'
import { EditNotice } from '@/features/manage-user'
import { ButtonIcon, HelpIcon } from '@/shared/ui'
import ChevroneMediumIcon from '@/shared/ui/Icon/icons/components/ChevroneMediumIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import Skeleton from 'react-loading-skeleton'
import { EducationChartWidget, RiskLevelChartWidget } from '@/features/graphics'
import { PhishingStatistics } from '@/shared/components/phishing-statistics'
import { Link, useParams } from 'react-router-dom'
import { AssignedCoursesByUser } from '@/shared/components/assigned-courses-by-user/assigned-courses-by-user'
import { Trans } from 'react-i18next'
import LazyRiskLevelModal from '@/shared/modals/risk-level-modal/lazy-risk-level-modal'
import LazyEmployeeSettingsModal from '@/shared/modals/employee-settings-modal/lazy-employee-settings-modal'

const cx = classNamesBind.bind(styles)

export interface IEmployeeStatisticsProps {
  className?: string
}

const EmployeeStatistics: React.FC<IEmployeeStatisticsProps> = props => {
  const { className } = props
  const {
    info,
    t,
    educationTimeRangeItem,
    setEducationTimeRangeItem,
    setRiskLevelTimeRangeItem,
    onPhishingExcelClick,
    risk_level_chart,
    educationData,
    phishing_chart,
  } = useEmployeeStatistics()
  const { employee_id = '' } = useParams()
  const [openRiskLevelModal, setOpenRiskLevelModal] = useState<boolean>(false)
  const [openSettingsModal, setOpenSettingsModal] = useState<boolean>(false)

  return (
    <div className={cx('wrapper', className)}>
      <h1 className={cx('title')}>
        {info?.last_name} {info?.first_name} {info?.middle_name}
        {info?.two_fa && (
          <ButtonIcon
            onClick={() => setOpenSettingsModal(true)}
            iconSize='24'
            size='32'
            color='gray70'
            icon='settingsSmall'
          />
        )}
      </h1>
      <div className={cx('information')}>
        {info?.id ? (
          <EmployeeInformation
            wrapperClassName={cx('general')}
            attributes={[
              {
                name: t('fio'),
                content: getFullUserName({
                  firstName: info?.first_name ?? '',
                  lastName: info?.last_name ?? '',
                  middleName: info?.middle_name ?? '',
                })
                  ? getFullUserName({
                      firstName: info?.first_name ?? '',
                      lastName: info?.last_name ?? '',
                      middleName: info?.middle_name ?? '',
                    })
                  : '—',
              },
              { name: t('email'), content: `${info?.email}` },
              { name: t('department'), content: `${info?.department?.title}` },
              {
                name: t('position'),
                content: info?.position ? info?.position : '—',
              },
              { name: t('role'), content: info?.role ? t(`roles.${info?.role}`) : '—' },
              {
                name: t('tags'),
                content: <Tags tags={info?.tags ?? []} />,
              },
            ]}
          />
        ) : (
          <div className={cx('general')}>
            <Skeleton borderRadius={'16px'} height={'340px'} />
          </div>
        )}
        <EmployeeNotice
          wrapperClassName={cx('notice')}
          title={t('notice')}
          textarea={{ value: info?.note ?? '', disabled: true }}
          actions={
            info?.id && (
              <div className={cx('note')}>
                <EditNotice note={info?.note ?? ''} userId={info?.id} />
              </div>
            )
          }
        />
      </div>
      <div className={cx('statistics')}>
        <h3 className={cx('title')}>{t('statistics')}</h3>
        <div className={cx('widgets')}>
          <RiskLevelChartWidget
            isLoading={!risk_level_chart?.[0]?.data}
            chartProps={{
              data: risk_level_chart?.[0]?.data ?? [],
              type: 'redToGreen',
              customMax: 10,
              customMin: 0,
              dimension: '',
            }}
            descriptionProps={{
              risk: Number(info?.statistic?.risk_level?.toFixed(1)) ?? 0,
              showHelpIcon: false,
            }}
            onTimeRangeChange={v => setRiskLevelTimeRangeItem(v)}
            infoClassName={cx('widgetInfo')}
            bottomContent={
              <div className={cx('widgetHint')}>
                <HelpIcon
                  tooltipClassname={cx('tooltip')}
                  text={
                    <Trans
                      i18nKey={'commons:risk_level_hint'}
                      t={t}
                      components={{
                        button: (
                          <div className={cx('open')} onClick={() => setOpenRiskLevelModal(true)} />
                        ),
                      }}
                    />
                  }
                />
                {t('hints.risk')}
              </div>
            }
          />
          <EducationChartWidget
            isLoading={!educationData}
            chartProps={{
              data: educationData,
              customMin: 0,
            }}
            descriptionProps={{
              percent: educationData[0]?.value.toFixed(1) ?? 0,
            }}
            onTimeRangeChange={v => setEducationTimeRangeItem(v)}
            defaultItem={educationTimeRangeItem}
            infoClassName={cx('widgetInfo')}
          />
          <PhishingStatistics
            {...phishing_chart}
            statsClassName={cx('phishingStats')}
            onClick={onPhishingExcelClick}
            isHiddenDownloadBtn
          />
        </div>
      </div>
      <div className={cx('coursesByUser')}>
        <AssignedCoursesByUser
          employeeId={employee_id}
          statPosition='middle'
          howMuchMaxToShow={3}
          titleWhenCanShowMore={
            <div className={cx('check')}>
              <Link
                className={cx('link')}
                to={`/lk/admin/staff/employees/${employee_id}/assigned-courses`}
              >
                {t('check_all')}
              </Link>
              <IconWrapper color='gray80'>
                <ChevroneMediumIcon />
              </IconWrapper>
            </div>
          }
        />
      </div>
      {openSettingsModal && info?.two_fa && (
        <LazyEmployeeSettingsModal
          open={openSettingsModal}
          setOpen={setOpenSettingsModal}
          employeeInfo={info}
        />
      )}
      {openRiskLevelModal && (
        <LazyRiskLevelModal open={openRiskLevelModal} setOpen={setOpenRiskLevelModal} />
      )}
    </div>
  )
}

export default EmployeeStatistics
