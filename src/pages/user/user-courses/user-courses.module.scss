.wrapper {
  display: grid;
  grid-template-rows: auto 1fr;

  min-height: 100%;
}

.card {
  height: 100%;
  position: relative;
  cursor: pointer;
}

.courses {
  &__wrapper {
    display: grid;
    grid-gap: 16px;
    grid-template-columns: repeat(3, 1fr);

    max-width: 914px;

    @media (max-width: 1280px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (max-width: 576px) {
      grid-template-columns: repeat(1, 1fr);
    }

    &.flex {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &__testing {
    display: flex;
    justify-content: space-between;
    gap: 8px;

    margin-top: 24px;

    color: var(--color-gray-80);
    font: var(--font-caption-1-normal);

    &__progress {
      display: flex;
      flex-direction: column;
      gap: 4px;
      text-align: right;
    }
  }

  &_completed {
    opacity: 0.5;
    pointer-events: none;
  }
}

.title-wrapper {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
  .title {
    margin-bottom: 0;
  }

  @media (max-width: 1024px) {
    flex-direction: column;
    align-items: flex-start;

    .title {
      margin-bottom: 8px;
    }

    .getCert {
      background: transparent;
      border: none;
      color: var(--color-primary);
      font: var(--font-text-2-demibold);
      padding: 0;
    }
  }
}

.tabs {
  margin-bottom: 16px;

  @media (max-width: 768px) {
    max-width: 100%;
    overflow-y: scroll;
    padding: 12px 0;
    gap: 20px;
  }

  &__item {
    @media (max-width: 768px) {
      font: var(--font-title-2-normal);
    }
  }
}

.filterTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.courseTopAdornment {
  position: absolute;
  top: 0px;
  left: 0px;
  padding: 20px;
  z-index: 1;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
