import { ReactNode } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import classNamesBind from 'classnames/bind'
import styles from './assigned-course-card.module.scss'
import Graduation from '@/shared/ui/Icon/icons/components/Graduation'
import ChevroneSmallIcon from '@/shared/ui/Icon/icons/components/ChevroneSmallIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

type Props = {
  to: string
  title: string
  pictureSrc?: string | null
  footerCols?: { id: UUID; title?: ReactNode; value?: ReactNode; columnClassName?: string }[]
  contentSlot?: ReactNode
}

export const AssignedCourseCard = ({ to, title, pictureSrc, footerCols, contentSlot }: Props) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  return (
    <div role='link' onClick={() => navigate(to)} className={cx('course')}>
      <div className={cx('actions')}>
        <div className={cx('pictureWrapper')}>
          {pictureSrc ? (
            <img src={pictureSrc} alt={title} className={cx('picture')} />
          ) : (
            <IconWrapper color='gray70' size='32'>
              <Graduation />
            </IconWrapper>
          )}
        </div>
        <Link to={to}>
          <button className={cx('chevrone')}>
            <span className={cx('chevroneText')}>{t('commons:more_details')}</span>
            <IconWrapper size='24'>
              <ChevroneSmallIcon />
            </IconWrapper>
          </button>
        </Link>
      </div>
      {contentSlot}
      <p className={cx('title')}>{title}</p>
      {footerCols && (
        <div className={cx('footerColumns')}>
          {footerCols.map(column => (
            <div className={cx('footerColumn', column.columnClassName)} key={column.id}>
              <p className={cx('footerRow')}>{column.title}</p>
              <p className={cx('footerRow')}>{column.value}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
