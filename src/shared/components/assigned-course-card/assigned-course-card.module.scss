@use '../../../shared/assets/styles/mixins/text';
@use '../../../shared/assets/styles/mixins/colors';
@use '../../../shared/assets/styles/mixins/media';

.course {
  padding: 20px 20px 16px 20px;
  background-color: white;
  min-height: 180px;
  border: 1px solid color(--color-gray-30);
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  cursor: pointer;
  &:hover {
    .chevroneText {
      display: block;
    }

    * {
      stroke: var(--color-primary) !important;
    }
  }
}

.actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.pictureWrapper {
  background-color: var(--color-gray-30);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.picture {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 16px;
  border: 1px solid var(--color-gray-30);
  max-width: 100%;
  max-height: 100%;
  height: 100%;
}

.chevrone {
  display: flex;
  align-items: center;
  gap: 4px;
}

.chevroneText {
  display: none;
  font: var(--font-caption-1-medium);
  color: var(--color-primary);
}

.title {
  color: var(--color-gray-90);
  font: var(--font-text-2-demibold);
  word-break: break-word;
  @include text.max-lines(2);
  height: 40px;
}

.courseTags {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
  justify-content: center;
}

.footerColumns {
  display: flex;
  flex-direction: column;
  margin-top: auto;
  gap: 4px;
}

.footerColumn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footerRow {
  text-align: right;
  color: var(--color-gray-80);
  font: var(--font-caption-1-medium);
  font-weight: 500;
}
