import { lazy, Suspense } from 'react'
import { EmployeeSettingsModalProps } from './employee-settings-modal.d'

const EmployeeSettingsModal = lazy(() => import('./employee-settings-modal'))

const LazyEmployeeSettingsModal: React.FC<EmployeeSettingsModalProps.Props> = props => {
  return (
    <Suspense fallback={<></>}>
      <EmployeeSettingsModal {...props} />
    </Suspense>
  )
}

export default LazyEmployeeSettingsModal
