import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import { EmployeeSettingsModalProps } from './employee-settings-modal.d'
import { Modal } from '@/shared/components'
import { Button, HelpIcon } from '@/shared/ui'
import { authAPI } from '@/entities/auth'
import { useNotification } from '@/shared/contexts/notifications'
import classNamesBind from 'classnames/bind'
import styles from './employee-settings-modal.module.scss'

const cx = classNamesBind.bind(styles)
const TRANSLATION_FILE = 'modals__employee-settings-modal'

const EmployeeSettingsModal: React.FC<EmployeeSettingsModalProps.Props> = ({ open, setOpen }) => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const { employee_id = '' } = useParams()
  const { handleErrorResponse } = useNotification()
  const [isLoading, setIsLoading] = useState(false)

  const [triggerTwoFaForceDeactivate] = authAPI.useTwofaForceDeactivateMutation()

  const handleResetTwoFA = async () => {
    if (!employee_id) return

    setIsLoading(true)
    try {
      await triggerTwoFaForceDeactivate({ user_id: employee_id }).unwrap()
      setOpen(false)
    } catch (error) {
      handleErrorResponse({
        status: 'error',
        message: t('reset_twofa_error'),
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Modal setActive={v => setOpen(Boolean(v))} active={open} className={cx('modal')}>
      <h3 className={cx('modal__title')}>{t('title')}</h3>
      <div className={cx('content')}>
        <Button onClick={handleResetTwoFA} loading={isLoading}>
          {t('reset_twofa_button')}
        </Button>
        <HelpIcon tooltipClassname={cx('tooltip')} text={t('reset_twofa_hint')} />
      </div>
    </Modal>
  )
}

export default EmployeeSettingsModal
