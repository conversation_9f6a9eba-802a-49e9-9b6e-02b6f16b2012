.wrapper {
  display: grid;
  grid-gap: 32px;
  grid-template-rows: auto 1fr auto;
  max-height: fit-content;
  max-width: 624px;
  width: 100%;
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-4-normal);
}

.tab-selector-wrapper {
  margin-bottom: 16px;
}

.buttonWrapper {
  display: flex;
  gap: 16px;
  justify-content: end;
}

.file {
  left: -999px;

  opacity: 0;

  pointer-events: none;
  position: absolute;
}

.inner {
  align-items: center;

  display: flex;
  justify-content: center;
  min-height: 250px;
  position: relative;
  .bg {
    left: 50%;
    position: absolute;
    top: 50%;

    transform: translate(-50%, -50%);
    z-index: 4;
  }
}

.info {
  position: relative;
  z-index: 5;

  &.first {
    margin-top: 35px;

    .addButton {
      display: block;
      margin: 0 auto;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;

      white-space: nowrap;
    }

    .help {

      color: var(--color-gray-80, #5c6585);
      display: block;
      font: var(--font-caption-2-normal);
      margin-top: 12px;
      text-align: center;
    }
    .customButton {
      margin-top: 40px;
    }
  }
}

.customButton {
  align-items: center;

  color: var(--color-gray-80, #5c6585);

  cursor: pointer;
  display: flex;
  font: var(--font-text-2-medium);
  gap: 4px;
  justify-content: center;
}

.info.second {
  .loader {
    margin-bottom: 3px;
  }
  .loaderTitle {

    color: var(--color-gray-90, #343b54);
    font: var(--font-text-2-normal);
    margin-bottom: 24px;
    text-align: center;
  }
  .help {

    color: var(--color-gray-80, #5c6585);
    display: block;
    font: var(--font-caption-2-normal);
    max-width: 180px;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.info.error,
.info.success {
  align-self: start;
  display: grid;
  grid-gap: 54px;
  grid-template-columns: 154px 1fr;
  justify-content: space-between;
  max-width: 540px;
  width: 100%;
  .bgWrapper {
    position: relative;
    .bg {
      position: static;

      transform: none;
    }
    .bgText {
      left: 50%;

      margin-top: 10px;
      position: absolute;
      top: 50%;

      transform: translate(-50%, -50%);
      z-index: 4;
      > span {
        display: block;

        font: var(--font-text-2-normal);
        margin-bottom: 24px;
        text-align: center;
      }
      > .help {

        color: var(--color-gray-80, #5c6585);
        display: block;
        font: var(--font-caption-2-normal);
        max-width: 112px;
        overflow: hidden;
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .customButton {
    margin-top: 17px;
  }
}

.info.error {
  .bgWrapper {
    .bgText {
      > span {
        color: var(--color-error, #ff4b60);
      }
    }
  }
  .errorTitle {

    color: var(--color-error, #ff4b60);
    font: var(--font-text-2-medium);
    margin-bottom: 12px;
  }
}

.info.success {
  .title {

    color: var(--color-gray-90, #343b54);
    font: var(--font-text-2-medium);
    margin-bottom: 12px;
  }
  .items {
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .item {

    color: var(--color-gray-80, #5c6585);
    font: var(--font-text-2-normal);
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
    span {
      color: var(--color-gray-80, #5c6585);
      font: var(--font-text-2-medium);
    }
  }

  .info.import {
    .text {
      color: var(--color-primary, #3dbc87);
      font: var(--font-text-2-normal);
      text-align: center;
      &.bad {
        color: var(--color-error, #ff4b60);
      }
    }
  }

  .checkboxWrapper {
    grid-column: 1/3;
    .checkboxInner {
      align-items: center;
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
      span {
        color: var(--color-gray-80, #5c6585);
        font: var(--font-text-2-normal);
      }
    }
  }
}

.hint {
  color: var(--color-gray-70);
  font: var(--font-text-2-medium);
  text-align: center;
  margin-top: 16px;
  &_underline {
    color: var(--color-gray-90);

    text-decoration: underline;
  }
}

.text {
  text-align: center;
}