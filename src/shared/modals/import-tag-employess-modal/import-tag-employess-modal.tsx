/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { FC, useRef, useState } from 'react'
import styles from './import-tag-employess-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ImportTagEmployeesModalProps, TagOperationMode } from './import-tag-employess-modal.d'
import { Modal } from '@/shared/components'
import { Button, Loader, Switch } from '@/shared/ui'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { IEmployeesTagImport } from 'entities/employee'
import { useConfig, useDownload } from '@/shared/hooks'
import { combineURLs } from '@/shared/helpers'
import {
  useImportTagEmployeesWithFileInfoMutation,
  useImportTagEmployeesWithFileMutation,
  useRemoveTagEmployeesWithFileInfoMutation,
  useRemoveTagEmployeesWithFileMutation,
} from '@/store/services/tags-employees-service'
import { selectActiveTag } from '@/store/slices/tags'
import { useAppSelector } from '@/store'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const OPERATION_MODE_ASSIGN: TagOperationMode = 'assign'
const OPERATION_MODE_REMOVE: TagOperationMode = 'remove'

export const ImportTagEmployeesModal: FC<ImportTagEmployeesModalProps.Props> = props => {
  const { className, active, setActive, refetchUsers } = props
  const activeTag = useAppSelector(selectActiveTag)
  const { t } = useTranslation('components__import-tag-employees-modal')

  const csvInput = useRef<HTMLInputElement | null>(null)

  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [operationMode, setOperationMode] = useState<TagOperationMode>(OPERATION_MODE_ASSIGN)

  const onCsvInputClick = () => {
    csvInput.current?.click()
  }

  const handleAdd = () => {
    if (step === 0 && csvFile) {
      setStep(() => 1)
      getInfoaAboutFile()
    }

    if (step === 1) {
      handleImport()
    }
  }
  const handleReset = () => {
    setStep(() => 0)
    setCsvFile(() => null)
    setGetInfoError(() => '')
    setGetInfoInfo(() => null)
    if (csvInput?.current?.value) csvInput.current.value = ''
  }

  const config = useConfig()
  const [download] = useDownload()

  const onCsvTemplateClick = () => {
    download(
      combineURLs(
        config?.url?.startsWith(`https://`) ? config?.url || '' : `https://` + config?.url || '',
        '/lk/assets/legacy_files/tag_users_template.xlsx',
      ),
      'tag_users_template.xlsx',
    )
  }

  const onCsvFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files ? e.target.files[0] : undefined

    if (!file) return

    setCsvFile(file)
  }

  const [step, setStep] = useState(0)

  const [getInfo, { isLoading: isLoadingGetInfo }] = useImportTagEmployeesWithFileInfoMutation()
  const [importFile, { isLoading: isLoadingImportFile, error: errorImportFile }] =
    useImportTagEmployeesWithFileMutation()
  const [getRemoveInfo, { isLoading: isLoadingGetRemoveInfo }] =
    useRemoveTagEmployeesWithFileInfoMutation()
  const [removeFile, { isLoading: isLoadingRemoveFile, error: errorRemoveFile }] =
    useRemoveTagEmployeesWithFileMutation()

  const [getInfoError, setGetInfoError] = useState('')
  const [getInfoInfo, setGetInfoInfo] = useState<IEmployeesTagImport | null>(null)

  const getInfoaAboutFile = async () => {
    if (!csvFile || !activeTag?.id) return

    const formData = new FormData()

    formData.append('upload_file', csvFile)

    try {
      const apiCall = operationMode === OPERATION_MODE_ASSIGN ? getInfo : getRemoveInfo
      await apiCall({ body: formData, tag_id: activeTag.id })
        .unwrap()
        .then(response => setGetInfoInfo(response))
        .catch(e => {
          setGetInfoError(e.message || e?.data?.message || e.error || t('commons:error_unexpected'))
        })
    } catch (e: any) {
      setGetInfoError(e.message || e?.data?.message || e.error || t('commons:error_unexpected'))
    }
  }

  const handleImport = async () => {
    if (!csvFile || !activeTag?.id) return

    const formData = new FormData()

    formData.append('upload_file', csvFile)
    setStep(() => 2)

    try {
      const apiCall = operationMode === OPERATION_MODE_ASSIGN ? importFile : removeFile
      await apiCall({
        body: formData,
        tag_id: activeTag.id,
      }).unwrap()
      refetchUsers && refetchUsers()
    } catch (e: any) {
      setGetInfoError(e.message || e?.data?.message || e.error || t('commons:error_unexpected'))
    }
  }

  return (
    <>
      <Modal active={active} setActive={setActive} className={cx('wrapper', className)}>
        <div className={cx('title')}>{t('import_from_file')}</div>
        <div className={cx('switch-wrapper')}>
          <Switch
            customValue={operationMode === OPERATION_MODE_ASSIGN}
            onChange={() =>
              setOperationMode(
                operationMode === OPERATION_MODE_ASSIGN
                  ? OPERATION_MODE_REMOVE
                  : OPERATION_MODE_ASSIGN,
              )
            }
            text=''
          />
          <label className={cx('switch-label')}>
            {operationMode === OPERATION_MODE_ASSIGN ? t('assign_tag') : t('remove_tag')}
          </label>
        </div>
        <div className={cx('inner')}>
          {(step === 0 ||
            (step === 1 && csvFile?.name && (isLoadingGetInfo || isLoadingGetRemoveInfo))) && (
            <svg
              className={cx('bg')}
              width='194'
              height='246'
              viewBox='0 0 194 246'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M16 246H178C186.837 246 194 238.837 194 230V59.359C194 55.1053 192.306 51.0267 189.293 48.0244L145.774 4.66544C142.775 1.67762 138.714 0 134.481 0H16C7.16344 0 0 7.16344 0 16V230C0 238.837 7.16345 246 16 246Z'
                fill='#F0F3F7'
              />
              <path
                fillRule='evenodd'
                clipRule='evenodd'
                d='M138.165 0H128V54.0004C128 60.6278 133.373 66.0004 140 66.0004H194V57.8156C194 54.624 192.729 51.5639 190.467 49.312L144.204 3.24811C142.537 1.58894 140.434 0.468927 138.165 0Z'
                fill='#E1E4EB'
              />
            </svg>
          )}
          {step === 0 && (
            <div className={cx('info', 'first')}>
              <Button
                size='small'
                onClick={() => {
                  onCsvInputClick()
                }}
                className={cx('addButton')}
              >
                {csvFile ? csvFile.name : t('choose_file')}
              </Button>
              <span className={cx('help')}>({t('xlsx_format')})</span>

              <div className={cx('customButton')} onClick={onCsvTemplateClick}>
                {t('download_example')}
                <IconWrapper>
                  <DownloadIcon />
                </IconWrapper>
              </div>
            </div>
          )}
          {step === 1 && csvFile?.name && (isLoadingGetInfo || isLoadingGetRemoveInfo) && (
            <div className={cx('info', 'second')}>
              <Loader size='56' className={cx('loader')} loading />
              <div className={cx('loaderTitle')}>{t('commons:loading')}</div>
              <span className={cx('help')}>{csvFile.name}</span>
            </div>
          )}
          {step === 1 && csvFile?.name && !!getInfoError && !getInfoInfo && (
            <div className={cx('info', 'error')}>
              <div>
                <div className={cx('bgWrapper')}>
                  <svg
                    className={cx('bg')}
                    width='154'
                    height='195'
                    viewBox='0 0 154 195'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M16 195H138C146.837 195 154 187.837 154 179V48.4371C154 44.1795 152.303 40.0974 149.285 37.0945L116.681 4.65739C113.683 1.67449 109.626 0 105.397 0H16C7.16344 0 0 7.16344 0 16V179C0 187.837 7.16344 195 16 195Z'
                      fill='#F0F3F7'
                    />
                    <path
                      fillRule='evenodd'
                      clipRule='evenodd'
                      d='M109.203 0H102V40C102 46.6274 107.373 52 114 52H154V46.609C154 43.4174 152.728 40.3573 150.467 38.1054L115.511 3.30078C113.779 1.57586 111.575 0.43368 109.203 0Z'
                      fill='#E1E4EB'
                    />
                  </svg>
                  <div className={cx('bgText')}>
                    <Loader size='56' className={cx('loader')} error loading={false} />
                    <span>{t('commons:error')}</span>
                    <span className={cx('help')}>{csvFile.name}</span>
                  </div>
                </div>
                <div className={cx('customButton')} onClick={handleReset}>
                  <IconWrapper>
                    <ClipBoldIcon />
                  </IconWrapper>
                  {t('change_file')}
                </div>
              </div>
              <div>
                <span className={cx('errorTitle')}>{t('table_error')}</span>
              </div>
            </div>
          )}
          {step === 1 && csvFile?.name && !!getInfoInfo && (
            <div className={cx('info', 'success')}>
              <div>
                <div className={cx('bgWrapper')}>
                  <svg
                    className={cx('bg')}
                    width='154'
                    height='195'
                    viewBox='0 0 154 195'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M16 195H138C146.837 195 154 187.837 154 179V48.4371C154 44.1795 152.303 40.0974 149.285 37.0945L116.681 4.65739C113.683 1.67449 109.626 0 105.397 0H16C7.16344 0 0 7.16344 0 16V179C0 187.837 7.16344 195 16 195Z'
                      fill='#F0F3F7'
                    />
                    <path
                      fillRule='evenodd'
                      clipRule='evenodd'
                      d='M109.203 0H102V40C102 46.6274 107.373 52 114 52H154V46.609C154 43.4174 152.728 40.3573 150.467 38.1054L115.511 3.30078C113.779 1.57586 111.575 0.43368 109.203 0Z'
                      fill='#E1E4EB'
                    />
                  </svg>
                  <div className={cx('bgText')}>
                    <Loader
                      size='56'
                      className={cx('loader')}
                      success
                      error={false}
                      loading={false}
                    />
                    <span>{t('commons:downloaded')}</span>
                    <span className={cx('help')}>{csvFile.name}</span>
                  </div>
                </div>
                <div className={cx('customButton')} onClick={handleReset}>
                  <IconWrapper>
                    <ClipBoldIcon />
                  </IconWrapper>
                  {t('change_file')}
                </div>
              </div>
              <div>
                <span className={cx('title')}>{t('users_data')}</span>

                <div className={cx('items')}>
                  {typeof getInfoInfo.to_assign_count === 'number' && (
                    <div className={cx('item')}>
                      {operationMode === OPERATION_MODE_ASSIGN
                        ? t('users_to_assign')
                        : t('users_to_remove')}
                      : {getInfoInfo.to_assign_count}
                    </div>
                  )}
                  {typeof getInfoInfo.unknown_count === 'number' && (
                    <div className={cx('item')}>
                      {t('unknown_users')}: {getInfoInfo.unknown_count}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          {step === 2 && (
            <div className={cx('info', 'import')}>
              <Loader
                size='56'
                loading={isLoadingImportFile || isLoadingRemoveFile}
                error={!!(errorImportFile || errorRemoveFile)}
                success={
                  !(errorImportFile || errorRemoveFile) &&
                  !(isLoadingImportFile || isLoadingRemoveFile)
                }
              />
              <div
                className={cx('text', {
                  bad:
                    !(isLoadingImportFile || isLoadingRemoveFile) &&
                    !!(errorImportFile || errorRemoveFile),
                })}
              >
                {(isLoadingImportFile || isLoadingRemoveFile) &&
                  !(errorImportFile || errorRemoveFile) &&
                  t('import_process')}
                {!(isLoadingImportFile || isLoadingRemoveFile) &&
                  !!(errorImportFile || errorRemoveFile) &&
                  t('download_errort')}
                {!(isLoadingImportFile || isLoadingRemoveFile) &&
                  !(errorImportFile || errorRemoveFile) &&
                  t('import_finished')}
              </div>
              {!(isLoadingImportFile || isLoadingRemoveFile) &&
                !(errorImportFile || errorRemoveFile) && (
                  <div className={cx('hint')}>{t('hint')}</div>
                )}
            </div>
          )}
        </div>
        <div className={cx('buttonWrapper')}>
          {step === 2 &&
            !(isLoadingImportFile || isLoadingRemoveFile) &&
            !(errorImportFile || errorRemoveFile) && (
              <Button onClick={() => setActive(false)}>{t('commons:finish')}</Button>
            )}
          {step !== 2 && (
            <>
              <Button color='gray' onClick={handleReset}>
                {t('reset')}
              </Button>
              {!getInfoError && (
                <Button disabled={!csvFile && step === 0} onClick={handleAdd}>
                  {step === 1 && csvFile?.name && !!getInfoInfo
                    ? t('commons:submit')
                    : t('commons:add')}
                </Button>
              )}
            </>
          )}
        </div>
        <input
          //TODO move to hook
          className={cx('file')}
          ref={csvInput}
          type={'file'}
          onChange={onCsvFileChange}
          accept={'.xlsx'}
        />
      </Modal>
    </>
  )
}
