import { TagsEmployeesApiEndpointBuilder } from '../tags-employees-service'
import {
  ITag,
  ITagActionResp,
  ITagCourse,
  ITagCoursesWithData,
  ITagsWithData,
} from '@/shared/types/store/tag'
import { ICourse } from '@/shared/types/store/course'
import { IEmployeesTagImport } from 'entities/employee'
import { BACKEND_URLS } from '@/shared/constants/urls'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getTagsEndpoints = (api: any) => {
  return (build: TagsEmployeesApiEndpointBuilder) => ({
    getOrganizationTags: build.query<
      ITagsWithData,
      {
        limit: number
        offset: number
      }
    >({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/organizations`,
        params: body,
      }),
      providesTags: ['tags'],
    }),
    getCoursesByTag: build.query<ITagCourse[], UUID>({
      query: id => ({
        url: `/users/tags/${id}/courses`,
      }),
      transformResponse: (response: ITagCoursesWithData) => response.data,
      providesTags: ['courses'],
    }),
    getScormCoursesByTag: build.query<ITagCourse[], UUID>({
      query: id => ({
        url: `/users/tags/${id}/action/courses/scorm`,
      }),
      transformResponse: (response: ITagCoursesWithData) => response.data,
      providesTags: ['scormCourses'],
    }),
    getActionsByTag: build.query<ITagActionResp, UUID>({
      query: id => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/${id}/actions`,
      }),
      providesTags: ['messages'],
    }),
    updateMessage: build.mutation<
      void,
      { tagID: UUID; messageID: UUID; body: { theme: string; text: string } }
    >({
      query: ({ tagID, messageID, body }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/${tagID}/messages/${messageID}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['messages', 'courses', 'scormCourses'],
    }),
    createMessage: build.mutation<void, { body: { theme: string; text: string }; tagID: UUID }>({
      query: ({ body, tagID }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/${tagID}/messages`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['messages', 'courses', 'scormCourses'],
    }),
    deleteMessage: build.mutation<void, { tagID: UUID; messageID: UUID }>({
      query: ({ tagID, messageID }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/${tagID}/messages/${messageID}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['messages', 'courses', 'scormCourses'],
    }),
    updateLifeDays: build.mutation<ITag, { tagID: UUID; life_days: number }>({
      query: ({ tagID, life_days }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/default/${tagID}/life-days`,
        method: 'PATCH',
        body: { life_days },
      }),
      invalidatesTags: ['messages', 'courses', 'scormCourses'],
    }),
    updateRiskLevel: build.mutation<
      void,
      { tagID: UUID; min_risk_level: number; max_risk_level: number }
    >({
      query: ({ tagID, min_risk_level, max_risk_level }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/default/${tagID}/risk-group`,
        method: 'PATCH',
        body: { min_risk_level, max_risk_level },
      }),
      invalidatesTags: ['messages', 'courses', 'scormCourses'],
    }),
    createCourses: build.mutation<void, { tagID: UUID; coursesID: UUID[] }>({
      query: ({ tagID, coursesID }) => ({
        url: `/users/tags/action/courses`,
        method: 'POST',
        body: { tag_id: tagID, course_ids: coursesID },
      }),
      invalidatesTags: ['messages', 'courses'],
    }),
    createScormCourses: build.mutation<void, { tagID: UUID; coursesID: UUID[] }>({
      query: ({ tagID, coursesID }) => ({
        url: `/users/tags/action/courses/scorm`,
        method: 'POST',
        body: { tag_id: tagID, cmi_course_ids: coursesID },
      }),
      invalidatesTags: ['scormCourses'],
    }),
    updateCourses: build.mutation<void, { tagID: UUID; coursesID: UUID[]; courses: ICourse[] }>({
      query: ({ tagID, coursesID }) => ({
        url: `/users/tags/${tagID}/courses`,
        method: 'PATCH',
        body: { course_ids: coursesID },
      }),
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const respQueryFulfilled = await queryFulfilled
          if (respQueryFulfilled.meta?.response?.ok) {
            dispatch(api.util.updateQueryData('getCoursesByTag', arg.tagID, () => arg.courses))
          }
        } catch (err) {
          console.error(err)
        }
      },
      invalidatesTags: ['messages'],
    }),
    updateScormCourses: build.mutation<
      void,
      { tagID: UUID; coursesID: UUID[]; courses: ICourse[] }
    >({
      query: ({ tagID, coursesID }) => ({
        url: `/users/tags/${tagID}/courses/scorm`,
        method: 'PATCH',
        body: { cmi_course_ids: coursesID },
      }),
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const respQueryFulfilled = await queryFulfilled
          if (respQueryFulfilled.meta?.response?.ok) {
            dispatch(api.util.updateQueryData('getScormCoursesByTag', arg.tagID, () => arg.courses))
          }
        } catch (err) {
          console.error(err)
        }
      },
    }),
    deleteCourses: build.mutation<void, { tagID: UUID }>({
      query: ({ tagID }) => ({
        url: `/users/tags/${tagID}/action/courses`,
        method: 'DELETE',
      }),
      invalidatesTags: ['messages', 'courses'],
    }),
    deleteScormCourses: build.mutation<void, { tagID: UUID }>({
      query: ({ tagID }) => ({
        url: `/users/tags/${tagID}/action/courses/scorm`,
        method: 'DELETE',
      }),
      invalidatesTags: ['scormCourses'],
    }),
    toggleTag: build.mutation<void, { tagID: UUID; toggle: boolean }>({
      query: ({ tagID, toggle }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/toggle`,
        method: 'POST',
        body: { tag_id: tagID, toggle },
      }),
      invalidatesTags: ['messages', 'courses', 'tags', 'employeesTags', 'scormCourses'],
    }),
    createCustomTag: build.mutation<ITag, ITag>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/custom`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['employeesTags'],
    }),
    editCustomTag: build.mutation<ITag, { tagId: UUID; body: { title: string; color: string } }>({
      query: ({ tagId, body }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/${tagId}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['employeesTags'],
    }),
    deleteCustomTag: build.mutation<void, UUID>({
      query: tagId => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/${tagId}`,
        method: 'DELETE',
      }),
    }),
    importTagEmployeesWithFileInfo: build.mutation<
      IEmployeesTagImport,
      {
        body: FormData
        tag_id: string
      }
    >({
      query: ({ body, tag_id }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/${tag_id}/assign/xlsx/preview`,
        body,
        method: 'POST',
      }),
    }),
    importTagEmployeesWithFile: build.mutation<
      void,
      {
        body: FormData
        tag_id: string
      }
    >({
      query: ({ body, tag_id }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/${tag_id}/assign/xlsx`,
        body,
        method: 'POST',
      }),
    }),
    removeTagEmployeesWithFileInfo: build.mutation<
      IEmployeesTagImport,
      {
        body: FormData
        tag_id: string
      }
    >({
      query: ({ body, tag_id }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/${tag_id}/remove/xlsx/preview`,
        body,
        method: 'POST',
      }),
    }),
    removeTagEmployeesWithFile: build.mutation<
      void,
      {
        body: FormData
        tag_id: string
      }
    >({
      query: ({ body, tag_id }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/${tag_id}/remove/xlsx`,
        body,
        method: 'POST',
      }),
    }),
  })
}
