import {
  ILogin,
  ILoginLDAP,
  ILoginResponse,
  IPasswordRecovery,
  IPasswordRestore,
  IPasswordRestoreResponse,
  IPasswordResetTokenInfo,
  IRegistration,
  IRegistrationInfo,
  IRegistrationInfoResponse,
  IRegistrationResponse,
  IUpdatePasswordRequest,
  IUpdatePasswordResponse,
  IUserRegistrationInfo,
} from './types'
import { TwoFATestingRequest, TwoFAVerifyRequest } from '@/shared/types/store/twofa'
import { globalBaseApi } from 'store/services/endpoints/base'
import { BACKEND_URLS } from '@/shared/constants/urls'

const PREFIX_URL = '/lk/api/v2'

export const authAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    updatePassword: build.mutation<
      ResponseWithNotification<IUpdatePasswordResponse>,
      IUpdatePasswordRequest
    >({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/password/update`,
        method: 'POST',
        body,
      }),
      transformResponse: (response: IUpdatePasswordResponse) => ({
        ...response,
        notificationTip: 'tips:auth.password_update',
      }),
    }),
    login: build.mutation<ILoginResponse, ILogin>({
      query: ({ email, password }) => {
        const body = `grant_type=${encodeURIComponent(
          'password',
        )}&username=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`

        return {
          url: `/gateway/v1/authentication/oauth`,
          method: 'POST',
          body,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      },
    }),
    logOut: build.mutation<void, { domain_url: string }>({
      query: ({ domain_url }) => ({
        url: `${PREFIX_URL}/auth/logout`,
        method: 'POST',
        params: {
          domain_url,
        },
      }),
    }),
    loginLDAP: build.mutation<{ token: string }, ILoginLDAP>({
      query: body => ({
        url: `${PREFIX_URL}/auth/ldap`,
        method: 'POST',
        body,
      }),
    }),
    passwordRecovery: build.mutation<ResponseWithNotification<void>, IPasswordRecovery>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/password/reset`,
        method: 'POST',
        body,
      }),
      transformResponse: () => ({
        notificationTip: 'tips:auth.mail',
      }),
    }),
    restore: build.mutation<IPasswordRestoreResponse, IPasswordRestore>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/password/reset/confirm`,
        method: 'POST',
        body,
      }),
    }),
    resetToken: build.query<IPasswordResetTokenInfo, string>({
      query: token => ({
        url: `${BACKEND_URLS.USER_SERVICE}/password/reset/token/${token}`,
        method: 'GET',
      }),
    }),
    registration: build.mutation<ResponseWithNotification<IRegistrationResponse>, IRegistration>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/registration`,
        method: 'POST',
        body,
      }),
      transformResponse: (response: IRegistrationResponse) => ({
        ...response,
        notificationTip: 'tips:auth.registration',
      }),
    }),
    getRegistrationInfo: build.query<
      IUserRegistrationInfo & IRegistrationInfoResponse,
      IRegistrationInfo
    >({
      query: ({ invite_token }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/registration/info/token/${invite_token}`,
      }),
      transformResponse: (
        response: {
          user: IUserRegistrationInfo
        } & IRegistrationInfoResponse,
      ) => ({
        ...response,
        ...response.user,
      }),
    }),
    twofaCodeTesting: build.mutation<ResponseWithNotification<void>, TwoFATestingRequest>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/twofa/code/testing`,
        method: 'POST',
        body,
      }),
      transformResponse: (_, __, { notificationTip }) => ({
        notificationTip: notificationTip ? 'tips:auth.twofa.on' : '',
      }),
    }),
    twofaCodeVerify: build.mutation<ResponseWithNotification<void>, TwoFAVerifyRequest>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/twofa/code/verify`,
        method: 'POST',
        body,
      }),
      transformResponse: () => ({
        notificationTip: 'tips:auth.twofa.register',
      }),
    }),
    twofaQRCode: build.query<{ value: string }, void>({
      query: () => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/twofa/my-qr-link`,
        method: 'GET',
      }),
    }),
    twofaDeactivate: build.mutation<ResponseWithNotification<void>, { code: string }>({
      query: params => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/twofa/deactivate`,
        method: 'POST',
        params,
      }),
      transformResponse: () => ({
        notificationTip: 'tips:auth.twofa.off',
      }),
    }),
    twofaForceDeactivate: build.mutation<
      ResponseWithNotification<{ email: string; disactivated: boolean; message: string }>,
      { user_id: string }
    >({
      query: ({ user_id }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/${user_id}/twofa/deactivate/force`,
        method: 'POST',
      }),
      transformResponse: (response: { email: string; disactivated: boolean; message: string }) => ({
        ...response,
        notificationTip: 'tips:auth.twofa.force_deactivate',
      }),
    }),
    ssoLogin: build.query<{ token: string }, void>({
      query: () => ({
        url: `${PREFIX_URL}/auth/sso`,
        method: 'GET',
      }),
    }),
    generatePassword: build.mutation<
      { password: string },
      { organization_id: string; role: string }
    >({
      query: ({ organization_id, role }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/password/generate`,
        method: 'GET',
        params: {
          organization_id,
          role,
        },
      }),
    }),
  }),
})
