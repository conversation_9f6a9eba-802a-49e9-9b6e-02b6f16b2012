{"module": {"delete": "<PERSON><PERSON>le successfully deleted!", "copy": "Module successfully copied!", "create": "<PERSON><PERSON><PERSON> successfully created!", "update": "Module successfully updated!"}, "slide": {"audio_delete": "Audio successfully deleted!", "create": "Slide successfully created!", "update": "Slide successfully updated!", "delete": "Slide successfully deleted!"}, "question": {"create": "Question successfully created!", "create_many": "Questions successfully uploaded!", "update": "Question successfully updated!", "delete": "Question successfully deleted!"}, "employees": {"update": "Employee successfully updated!", "add": "Employee successfully added!", "create": "Employee successfully created!", "delete": "Employee successfully deleted!", "delete_many": "Employees successfully deleted!", "reinvite": "<PERSON><PERSON> successfully sent!", "note": {"update": "Employee note successfully updated!", "notification": "Your notification has been sent to the employees!"}}, "report": {"success": "Report successfully generated!"}, "ad": {"sync": "Synchronization successfully completed!"}, "course": {"complete": "Course successfully completed!", "create": "Course successfully created!", "assign": "Course successfully assigned!", "update": "Course successfully updated!", "delete": "Course successfully deleted!", "course_will_be_copied": "Course will be copied shortly", "course_will_be_shared": "Course will become publicly available shortly", "courses_will_be_deleted": "Courses will be deleted from the database shortly", "course_will_be_deleted": "Course will be deleted from the database shortly", "create_error": "An error occurred while creating the course.", "update_error": "An error occurred while updating the course.", "get_error": "Error retrieving course data", "section_title_update": "Change section title?", "sections_creation_error": "Error creating sections", "section_creation_error": "Error creating section", "section_creation_success": "Section successfully created", "section_update_error": "Error updating section", "section_title_update_success": "Title successfully updated!", "section_delete_error": "Error deleting section", "sections_delete_error": "Error deleting sections", "section_delete_success": "Section successfully deleted!", "sections_delete_success": "Sections will be deleted soon!", "section_order_error": "An error occurred while changing the order of the section", "section_order_success": "Order of sections successfully changed!", "transport_theme_success": "Theme successfully moved to another section!", "transport_theme_error": "Error moving theme to another section", "transport_themes_success": "Themes successfully moved to another section!", "transport_themes_error": "Error moving themes to another section", "swap_theme_error": "Error moving theme", "swap_theme_success": "Theme successfully moved!", "themes_delete_error": "Error deleting themes", "theme_delete_error": "Error deleting theme", "themes_delete_success": "Themes successfully deleted!", "theme_delete_success": "Theme successfully deleted!", "themes_append_error": "Error attaching themes", "themes_append_success": "Themes successfully attached!", "testing": {"complete": "Testing successfully completed!"}, "save_answer": "Answer successfully saved!"}, "auth": {"password_update": "Password successfully updated!", "mail": "<PERSON><PERSON> successfully sent!", "registration": "Registration was successful!", "twofa": {"submit": "You have confirmed two-factor authentication!", "off": "You have disabled two-factor authentication!", "on": "You have enabled two-factor authentication!", "register": "You have confirmed two-factor authentication!", "force_deactivate": "Employee's two-factor authentication settings have been reset!"}}, "mail": {"success": "Email successfully created!", "update": "Email successfully updated!"}, "pages": {"create": "Page successfully created!", "update": "Page successfully updated!", "delete": "Page successfully deleted!", "redirect": {"create": "Redirect page created!", "update": "Redirect page updated!", "delete": "Redirect page deleted!"}}, "department": {"create": "Department successfully created!", "update": "Department successfully updated!", "delete": "Department successfully deleted!"}, "templates": {"create": "Template successfully created!", "update": "Template successfully updated!", "delete": "Template successfully deleted!", "copy": "Template successfully copied!"}, "organizations": {"create": "Organization successfully created!", "update": "Organization successfully updated!", "delete": "Organization successfully deleted!"}, "phishing": {"on": "Auto-phishing successfully enabled!", "off": "Auto-phishing successfully disabled!", "save_action": "Action saved, mailing will be created within a minute!"}, "campaigns": {"create": "Mailing successfully created!", "update": "Mailing successfully updated!", "delete": "Mailing successfully deleted!", "copy": "Mailing successfully copied!", "end": "Mailing successfully completed!"}, "settings": {"update": "Settings successfully updated!", "ldap": {"update": "LDAP settings successfully updated!"}, "ad": {"update": "Periodic AD update settings updated!"}, "smtp": {"update": "SMTP settings successfully updated!"}, "adfs": {"update": "ADFS settings successfully updated!"}}, "users": {"avatar": "Avatar successfully updated!"}}